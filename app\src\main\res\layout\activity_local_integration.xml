<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/activity_main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:orientation="vertical">

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="left"
        android:layout_marginLeft="16dp"
        android:layout_marginTop="12dp"
        android:text="@string/select_option"
        android:textSize="16sp"
        android:textStyle="bold" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_weight="1">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/white"
            android:orientation="vertical">

            <Button
                android:id="@+id/checkout_button"
                style="@style/PrimaryActionButton"
                android:text="@string/payment_sample" />

            <Button
                android:id="@+id/list_orders_button"
                style="@style/PrimaryActionButton"
                android:text="@string/orders_list" />

            <Button
                android:id="@+id/find_orders_button"
                style="@style/PrimaryActionButton"
                android:text="@string/orders_find" />

            <Button
                android:id="@+id/cancel_payment_button"
                style="@style/PrimaryActionButton"
                android:text="@string/cancel_payment" />

            <Button
                android:id="@+id/print_sample_button"
                style="@style/PrimaryActionButton"
                android:text="@string/print_sample" />

        </LinearLayout>

    </ScrollView>

    <View
        android:layout_width="match_parent"
        android:layout_height="2dp"
        android:background="@color/black_alpha19" />

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            android:id="@+id/device_model_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:layout_margin="12dp"
            android:text="LIO"
            android:textSize="16dp"
            android:textStyle="bold" />

        <View
            android:layout_width="match_parent"
            android:layout_below="@+id/device_model_text"
            android:layout_height="1dp"
            android:background="@color/gray_button"/>


        <TextView
            android:id="@+id/ec_text"
            android:layout_width="wrap_content"
            android:layout_below="@+id/device_model_text"
            android:layout_height="wrap_content"
            android:layout_margin="12dp"
            android:text="E.C:"
            android:textSize="16dp"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/merchant_code_txt"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/device_model_text"
            android:layout_margin="12dp"
            android:layout_toRightOf="@+id/nl_text"
            android:text="00000000000000"
            android:textSize="16dp" />

        <TextView
            android:id="@+id/nl_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentLeft="true"
            android:layout_below="@+id/merchant_code_txt"
            android:layout_margin="12dp"
            android:text="Número Lógico:"
            android:textSize="16dp"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/logic_number_txt"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/merchant_code_txt"
            android:layout_margin="12dp"
            android:layout_toRightOf="@+id/nl_text"
            android:text="00000000000000"
            android:textSize="16dp" />

    </RelativeLayout>

</LinearLayout>
