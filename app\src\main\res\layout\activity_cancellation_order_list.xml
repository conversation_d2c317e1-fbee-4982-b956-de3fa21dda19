<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/content_main"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <TextView
        android:id="@+id/btn_list_orders"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        android:textAlignment="textStart"
        android:gravity="center"
        android:text="@string/select_order_to_cancel"
        android:textSize="16sp"
        android:textStyle="bold" />

    <View
        android:layout_width="match_parent"
        android:layout_height="2dp"
        android:layout_below="@id/btn_list_orders"
        android:background="@color/black_alpha19" />


    <com.cielo.ordermanager.sdk.RecyclerViewEmptySupport
        android:id="@+id/recycler_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@+id/btn_list_orders"
        android:layout_centerInParent="true" />

    <TextView
        android:id="@+id/empty_view"
        android:layout_below="@+id/btn_list_orders"
        android:layout_centerInParent="true"
        android:gravity="center"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"/>

</RelativeLayout>
