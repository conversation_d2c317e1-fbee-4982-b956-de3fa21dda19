<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:context=".MainActivity">

    <Button
        android:id="@+id/sdk_integration_button"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_marginTop="250dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        style="@style/PrimaryActionButton"
        android:text="@string/sdk_integration" />

    <Button
        android:id="@+id/deep_link_integration_button"
        style="@style/PrimaryActionButton"
        app:layout_constraintTop_toBottomOf="@+id/sdk_integration_button"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:text="@string/deep_link_integration" />

</androidx.constraintlayout.widget.ConstraintLayout>