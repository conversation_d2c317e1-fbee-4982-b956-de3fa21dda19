buildscript {

    ext {
        kotlin_version = '1.9.0'
        agp_version = '8.1.3'
    }
    repositories {
        mavenLocal()
        jcenter()
        mavenCentral()
        maven { url 'https://mapbox.bintray.com/mapbox' }
        google()
        maven {
            url artifactoryUrl
            credentials {
                username = artifactoryUser
                password = artifactoryPassword
            }
        }
    }

    dependencies {
        classpath "com.android.tools.build:gradle:$agp_version"
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
    }
}

allprojects {
    repositories {
        mavenLocal()
        jcenter()
        google()
        maven {
            url artifactoryUrl
            credentials {
                username = artifactoryUser
                password = artifactoryPassword
            }
        }
    }
}

task clean(type: Delete) {
    delete rootProject.buildDir
}


