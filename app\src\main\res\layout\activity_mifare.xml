<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
              xmlns:tools="http://schemas.android.com/tools"
              android:orientation="vertical"
              android:layout_width="match_parent"
              android:layout_height="match_parent">

    <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

        <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

            <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">
                <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Sector"/>
                <EditText
                        android:id="@+id/textSector"
                        android:layout_width="105dp"
                        android:layout_height="48dp"
                        android:inputType="number"
                        android:text="2" />
            </LinearLayout>

            <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">
                <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Block"/>
                <EditText
                        android:id="@+id/textBlock"
                        android:layout_width="105dp"
                        android:layout_height="48dp"
                        android:inputType="number"
                        android:text="0" />
            </LinearLayout>

            <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">
                <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="New Value"/>
                <EditText
                        android:id="@+id/textNewValue"
                        android:layout_width="match_parent"
                        android:layout_height="48dp"
                        android:inputType="number"
                        android:text="0" />
            </LinearLayout>
        </LinearLayout>

        <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">
            <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Card Data" />

            <TextView
                    android:id="@+id/cardData"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content" />
        </LinearLayout>

        <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:layout_marginTop="16dp"
                android:orientation="horizontal">
            <Button
                    android:id="@+id/readBtn"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Read"
                    />

            <Button
                    android:id="@+id/writeBtn"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:text="Write"
                    />

            <Button
                    android:id="@+id/backupBtn"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:text="Backup"
                    />

            <Button
                    android:id="@+id/deactivateBtn"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:text="Deactivate"
                    />
        </LinearLayout>

    </LinearLayout>


    <RelativeLayout
            android:id="@+id/insertCardView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="#E6424242"
            android:visibility="gone"
            tools:visibility="visible">

        <LinearLayout android:layout_width="wrap_content"
                      android:layout_height="wrap_content"
                      android:orientation="vertical"
                      android:layout_centerInParent="true">
            <ProgressBar
                    android:id="@+id/insertCardProgress"
                    android:visibility="visible"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:minHeight="40dp"
                    android:minWidth="40dp"/>
            <Button
                    android:id="@+id/cancelDetectBtn"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:onClick="deactivateBtnClick"
                    android:text="Cancel"
            />
        </LinearLayout>


    </RelativeLayout>



</RelativeLayout>