<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android">

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />

    <application
        android:name=".MyApplication"
        android:allowBackup="true"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:supportsRtl="true"
        android:theme="@style/AppTheme">
        <activity
            android:name=".sample.activities.ResponseActivity"
            android:exported="true" >

            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />

                <data
                    android:host="@string/intent_host"
                    android:scheme="@string/intent_scheme" />
            </intent-filter>
        </activity>

        <service
            android:name=".sample.services.DeepLinkService"
            android:exported="false"
            android:foregroundServiceType="dataSync" />

        <activity
            android:name=".sample.activities.DeepLinkIntegrationActivity"
            android:exported="false" />
        <activity
            android:name=".MainActivity"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name=".sample.activities.LocalIntegrationActivity"
            android:exported="true" />
        <activity android:name=".sample.activities.ListOrdersActivity" />
        <activity android:name=".sample.activities.CancelPaymentActivity" />
        <activity android:name=".sample.activities.CancellationOrderList" />
        <activity android:name=".sample.activities.PrintSampleActivity" />
        <activity android:name=".sample.activities.PaymentActivity" />
        <activity
            android:name=".sample.activities.FindOrdersActivity"
            android:windowSoftInputMode="adjustResize" />

        <receiver
            android:name=".receiver.LIOCancelationBroadcastReceiver"
            android:exported="true">
            <intent-filter>
                <action android:name="cielo.action.NOTIFY_TRANSACTION_CANCEL" />
            </intent-filter>
        </receiver>

<!--        caso esteja utilizando apenas a integração via deep link é necessário incluir essa tag-->
<!--        <meta-data-->
<!--            android:name="cs_integration_type"-->
<!--            android:value="uri" />-->
    </application>

</manifest>