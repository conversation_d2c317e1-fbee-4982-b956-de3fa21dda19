<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:context=".sample.activities.DeepLinkIntegrationActivity">

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="left"
        android:layout_marginLeft="16dp"
        android:layout_marginTop="12dp"
        android:text="@string/select_option"
        android:textSize="16sp"
        android:textStyle="bold" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/white"
            android:orientation="vertical">

            <Button
                android:id="@+id/dl_checkout_button"
                style="@style/PrimaryActionButton"
                android:text="@string/payment_sample" />

        </LinearLayout>

    </ScrollView>

</LinearLayout>