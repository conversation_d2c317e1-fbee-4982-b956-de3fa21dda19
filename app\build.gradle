apply plugin: 'com.android.application'
apply plugin: 'kotlin-android'
apply plugin: 'kotlin-parcelize'

buildscript {

    repositories {
        mavenCentral()
    }
    dependencies {
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
    }
}

android {
    namespace "com.cielo.ordermanager.sdk"
    lintOptions { abortOnError false }
    compileSdkVersion 33

    defaultConfig {
        applicationId "com.cielo.ordermanager.sdk.sample"
        minSdkVersion 25
        targetSdkVersion 33
        versionCode 3
        versionName "2.6"
        testInstrumentationRunner "android.support.test.runner.AndroidJUnitRunner"


        buildConfigField("String", "CREDENTIALS_CLIENT_ID", "\"xxxxxxxxxx\"") // insira sua client ID
        buildConfigField("String", "CREDENTIALS_ACCESS_TOKEN", "\"xxxxxxxxxx\"") // insira sua Credenciais de acesso

    }

    viewBinding {
        enabled = true
    }

    lintOptions {
        abortOnError false
    }

    buildTypes {
        release {
            minifyEnabled false

            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
    sourceSets { main { res.srcDirs = ['src/main/res', 'src/main/res/menu'] } }

    packagingOptions {
        exclude 'META-INF/*'
        exclude 'META-INF/LICENSE'
        exclude 'META-INF/NOTICE'
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }

    buildFeatures {
        buildConfig = true
    }
}

configurations.all {
    exclude group: 'com.android.support', module: 'support-v4'
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])

    /*
     * ------------- DEPENDENCIAS NECESSÁRIAS PRO ORDER MANAGER SDK -------------------
     * Seguir o procedimento da nossa documentação para importar a ultima versão da SDK:
     * https://developercielo.github.io/manual/cielo-lio#configurar-m%C3%B3dulos-e-depend%C3%AAncias
    */
    implementation 'com.cielo.lio:order-manager:2.7.2'
    // --------------------------------------------------------------------------- //

    implementation "org.jetbrains.kotlinx:kotlinx-coroutines-core:1.6.4"
    implementation "org.jetbrains.kotlinx:kotlinx-coroutines-android:1.6.4"

    implementation('com.mapbox.mapboxsdk:mapbox-android-sdk:6.7.1') {
        exclude group: 'com.android.support'
        exclude module: 'appcompat-v7'
        exclude module: 'support-v4'
    }
    implementation 'com.journeyapps:zxing-android-embedded:3.5.0'

    implementation 'androidx.appcompat:appcompat:1.3.1'
    implementation 'com.google.android.material:material:1.4.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.0'
    testImplementation 'junit:junit:4.13.2'
}